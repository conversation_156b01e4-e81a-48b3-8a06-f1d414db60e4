import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../../../store';
import { addNotification } from '../../../store/slices/uiSlice';
import { 
  Shield, 
  MessageSquare, 
  Users, 
  Send, 
  Plus, 
  Search,
  Bell,
  X,
  UserPlus
} from 'lucide-react';
import { apiService } from '../../../services/api';
import TabNavigation from '../shared/TabNavigation';

interface Student {
  id: number;
  username: string;
  first_name: string;
  last_name: string;
  display_name: string;
  email: string;
  student_id: string;
}

interface Conversation {
  id: number;
  title?: string;
  conversation_type: 'direct' | 'group' | 'course' | 'announcement';
  participants: any[];
  last_message?: any;
  unread_count: number;
  created_at: string;
  updated_at: string;
}

interface AdminMessagingState {
  activeTab: string;
  loading: boolean;
  conversations: Conversation[];
  students: Student[];
  selectedStudents: Student[];
  searchQuery: string;
  showNewMessageModal: boolean;
  showAnnouncementModal: boolean;
  messageForm: {
    title: string;
    content: string;
    recipients: number[];
    conversation_type: string;
  };
  announcementForm: {
    title: string;
    content: string;
    priority: string;
    target_audience: string;
  };
}

/**
 * Admin Messaging Interface
 * Full-featured admin messaging functionality
 */
const AdminMessagingInterface: React.FC = () => {
  const { t } = useTranslation();
  const { token } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();

  // State management
  const [state, setState] = useState<AdminMessagingState>({
    activeTab: 'conversations',
    loading: true,
    conversations: [],
    students: [],
    selectedStudents: [],
    searchQuery: '',
    showNewMessageModal: false,
    showAnnouncementModal: false,
    messageForm: {
      title: '',
      content: '',
      recipients: [],
      conversation_type: 'direct'
    },
    announcementForm: {
      title: '',
      content: '',
      priority: 'normal',
      target_audience: 'students'
    }
  });

  // Tab configuration
  const tabs = [
    {
      id: 'conversations',
      label: t('messaging.conversations', 'Conversations'),
      icon: MessageSquare,
      color: 'purple'
    },
    {
      id: 'students',
      label: t('messaging.students', 'Students'),
      icon: Users,
      color: 'blue'
    },
    {
      id: 'announcements',
      label: t('messaging.announcements', 'Announcements'),
      icon: Bell,
      color: 'red'
    }
  ];

  // Fetch data functions
  const fetchConversations = async () => {
    try {
      apiService.setToken(token);
      const response = await apiService.getConversations();
      setState(prev => ({
        ...prev,
        conversations: (response as any).results || response
      }));
    } catch (error) {
      console.error('Failed to fetch conversations:', error);
      dispatch(addNotification({
        type: 'error',
        title: t('messaging.error', 'Error'),
        message: t('messaging.fetchConversationsError', 'Failed to fetch conversations')
      }));
    }
  };

  const fetchStudents = async () => {
    try {
      apiService.setToken(token);
      const response = await apiService.getUsers({ role: 'student' });
      setState(prev => ({
        ...prev,
        students: (response as any).results || response
      }));
    } catch (error) {
      console.error('Failed to fetch students:', error);
      dispatch(addNotification({
        type: 'error',
        title: t('messaging.error', 'Error'),
        message: t('messaging.fetchStudentsError', 'Failed to fetch students')
      }));
    }
  };

  const fetchData = async () => {
    setState(prev => ({ ...prev, loading: true }));
    await Promise.all([fetchConversations(), fetchStudents()]);
    setState(prev => ({ ...prev, loading: false }));
  };

  useEffect(() => {
    fetchData();
  }, [token]);

  // Message sending functions
  const handleSendMessage = async () => {
    try {
      if (!state.messageForm.content.trim() || state.selectedStudents.length === 0) {
        dispatch(addNotification({
          type: 'error',
          title: t('messaging.error', 'Error'),
          message: t('messaging.messageFormIncomplete', 'Please fill in all required fields')
        }));
        return;
      }

      apiService.setToken(token);
      
      // Create conversation with selected students
      const conversationData = {
        title: state.messageForm.title || `Message to ${state.selectedStudents.length} student(s)`,
        conversation_type: 'direct',
        participant_emails: state.selectedStudents.map(s => s.email)
      };

      const conversation = await apiService.startConversation(conversationData);
      
      // Send the message
      const messageData = {
        content: state.messageForm.content,
        message_type: 'text'
      };

      await apiService.sendMessage((conversation as any).id, messageData);

      dispatch(addNotification({
        type: 'success',
        title: t('messaging.success', 'Success'),
        message: t('messaging.messageSent', 'Message sent successfully')
      }));

      // Reset form and close modal
      setState(prev => ({
        ...prev,
        showNewMessageModal: false,
        selectedStudents: [],
        messageForm: {
          title: '',
          content: '',
          recipients: [],
          conversation_type: 'direct'
        }
      }));

      // Refresh conversations
      fetchConversations();

    } catch (error) {
      console.error('Failed to send message:', error);
      dispatch(addNotification({
        type: 'error',
        title: t('messaging.error', 'Error'),
        message: t('messaging.sendMessageError', 'Failed to send message')
      }));
    }
  };

  const handleCreateAnnouncement = async () => {
    try {
      if (!state.announcementForm.title.trim() || !state.announcementForm.content.trim()) {
        dispatch(addNotification({
          type: 'error',
          title: t('messaging.error', 'Error'),
          message: t('messaging.announcementFormIncomplete', 'Please fill in all required fields')
        }));
        return;
      }

      apiService.setToken(token);
      
      const announcementData = {
        title: state.announcementForm.title,
        content: state.announcementForm.content,
        priority: state.announcementForm.priority,
        target_audience: state.announcementForm.target_audience,
        is_published: true
      };

      await apiService.createAnnouncement(announcementData);

      dispatch(addNotification({
        type: 'success',
        title: t('messaging.success', 'Success'),
        message: t('messaging.announcementCreated', 'Announcement created successfully')
      }));

      // Reset form and close modal
      setState(prev => ({
        ...prev,
        showAnnouncementModal: false,
        announcementForm: {
          title: '',
          content: '',
          priority: 'normal',
          target_audience: 'students'
        }
      }));

    } catch (error) {
      console.error('Failed to create announcement:', error);
      dispatch(addNotification({
        type: 'error',
        title: t('messaging.error', 'Error'),
        message: t('messaging.createAnnouncementError', 'Failed to create announcement')
      }));
    }
  };

  // Helper functions
  const toggleStudentSelection = (student: Student) => {
    setState(prev => ({
      ...prev,
      selectedStudents: prev.selectedStudents.find(s => s.id === student.id)
        ? prev.selectedStudents.filter(s => s.id !== student.id)
        : [...prev.selectedStudents, student]
    }));
  };

  const filteredStudents = state.students.filter(student =>
    student.display_name.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
    student.email.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
    student.student_id.toLowerCase().includes(state.searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Header */}
        <div className="mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-4">
              <div className="bg-purple-100 p-3 rounded-full">
                <Shield className="w-8 h-8 text-purple-600" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {t('messaging.adminCommunications', 'University Communications')}
                </h1>
                <p className="text-gray-600 mt-1">
                  {t('messaging.adminDesc', 'Manage university-wide communications and send messages to students')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="mb-6">
          <TabNavigation
            tabs={tabs}
            activeTab={state.activeTab}
            onTabChange={(tabId) => setState(prev => ({ ...prev, activeTab: tabId }))}
          />
        </div>

        {/* Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 min-h-[calc(100vh-20rem)]">
          <div className="p-6">
            {state.activeTab === 'conversations' && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">
                    {t('messaging.conversations', 'Conversations')}
                  </h3>
                  <button
                    onClick={() => setState(prev => ({ ...prev, showNewMessageModal: true }))}
                    className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center space-x-2"
                  >
                    <Plus className="w-4 h-4" />
                    <span>{t('messaging.newMessage', 'New Message')}</span>
                  </button>
                </div>

                {state.loading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
                    <p className="text-gray-500 mt-2">{t('messaging.loading', 'Loading conversations...')}</p>
                  </div>
                ) : state.conversations.length === 0 ? (
                  <div className="text-center py-12">
                    <MessageSquare className="w-16 h-16 mx-auto text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {t('messaging.noConversations', 'No Conversations')}
                    </h3>
                    <p className="text-gray-500">
                      {t('messaging.noConversationsDesc', 'Start a new conversation with students')}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {state.conversations.map((conversation) => (
                      <div
                        key={conversation.id}
                        className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                      >
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900">
                              {conversation.title || `Conversation ${conversation.id}`}
                            </h4>
                            <p className="text-sm text-gray-500 mt-1">
                              {conversation.participants.length} participant(s)
                            </p>
                            {conversation.last_message && (
                              <p className="text-sm text-gray-600 mt-2 truncate">
                                {conversation.last_message.content}
                              </p>
                            )}
                          </div>
                          <div className="text-right">
                            <span className="text-xs text-gray-400">
                              {new Date(conversation.updated_at).toLocaleDateString()}
                            </span>
                            {conversation.unread_count > 0 && (
                              <div className="bg-red-500 text-white text-xs rounded-full px-2 py-1 mt-1">
                                {conversation.unread_count}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {state.activeTab === 'students' && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">
                    {t('messaging.students', 'Students')} ({state.selectedStudents.length} selected)
                  </h3>
                  <div className="flex space-x-2">
                    {state.selectedStudents.length > 0 && (
                      <button
                        onClick={() => setState(prev => ({ ...prev, showNewMessageModal: true }))}
                        className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"
                      >
                        <Send className="w-4 h-4" />
                        <span>{t('messaging.sendMessage', 'Send Message')}</span>
                      </button>
                    )}
                  </div>
                </div>

                {/* Search */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder={t('messaging.searchStudents', 'Search students...')}
                    value={state.searchQuery}
                    onChange={(e) => setState(prev => ({ ...prev, searchQuery: e.target.value }))}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>

                {/* Selected students */}
                {state.selectedStudents.length > 0 && (
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <h4 className="font-medium text-purple-900 mb-2">
                      {t('messaging.selectedStudents', 'Selected Students')}
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {state.selectedStudents.map((student) => (
                        <span
                          key={student.id}
                          className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm flex items-center space-x-1"
                        >
                          <span>{student.display_name}</span>
                          <button
                            onClick={() => toggleStudentSelection(student)}
                            className="text-purple-600 hover:text-purple-800"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Students list */}
                {state.loading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
                    <p className="text-gray-500 mt-2">{t('messaging.loading', 'Loading students...')}</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {filteredStudents.map((student) => (
                      <div
                        key={student.id}
                        className={`border rounded-lg p-4 cursor-pointer transition-all ${
                          state.selectedStudents.find(s => s.id === student.id)
                            ? 'border-purple-500 bg-purple-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => toggleStudentSelection(student)}
                      >
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0">
                            <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                              <Users className="w-5 h-5 text-gray-600" />
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {student.display_name}
                            </p>
                            <p className="text-sm text-gray-500 truncate">
                              {student.email}
                            </p>
                            <p className="text-xs text-gray-400">
                              ID: {student.student_id}
                            </p>
                          </div>
                          {state.selectedStudents.find(s => s.id === student.id) && (
                            <div className="flex-shrink-0">
                              <div className="w-5 h-5 bg-purple-600 rounded-full flex items-center justify-center">
                                <UserPlus className="w-3 h-3 text-white" />
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {state.activeTab === 'announcements' && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">
                    {t('messaging.announcements', 'Announcements')}
                  </h3>
                  <button
                    onClick={() => setState(prev => ({ ...prev, showAnnouncementModal: true }))}
                    className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 flex items-center space-x-2"
                  >
                    <Plus className="w-4 h-4" />
                    <span>{t('messaging.newAnnouncement', 'New Announcement')}</span>
                  </button>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <Bell className="w-5 h-5 text-blue-600" />
                    <h4 className="font-medium text-blue-900">
                      {t('messaging.announcementInfo', 'Announcement Information')}
                    </h4>
                  </div>
                  <p className="text-blue-700 mt-2 text-sm">
                    {t('messaging.announcementDesc', 'Create university-wide announcements that will be visible to all students and faculty members.')}
                  </p>
                </div>

                <div className="text-center py-12">
                  <Bell className="w-16 h-16 mx-auto text-gray-300 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {t('messaging.createFirstAnnouncement', 'Create Your First Announcement')}
                  </h3>
                  <p className="text-gray-500">
                    {t('messaging.announcementHelp', 'Use announcements to communicate important information to the entire university community.')}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* New Message Modal */}
        {state.showNewMessageModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    {t('messaging.newMessage', 'New Message')}
                  </h3>
                  <button
                    onClick={() => setState(prev => ({ ...prev, showNewMessageModal: false }))}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('messaging.messageTitle', 'Message Title')} ({t('messaging.optional', 'Optional')})
                    </label>
                    <input
                      type="text"
                      value={state.messageForm.title}
                      onChange={(e) => setState(prev => ({
                        ...prev,
                        messageForm: { ...prev.messageForm, title: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder={t('messaging.messageTitlePlaceholder', 'Enter message title...')}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('messaging.recipients', 'Recipients')} ({state.selectedStudents.length} selected)
                    </label>
                    <div className="bg-gray-50 border border-gray-300 rounded-lg p-3 max-h-32 overflow-y-auto">
                      {state.selectedStudents.length === 0 ? (
                        <p className="text-gray-500 text-sm">
                          {t('messaging.noRecipientsSelected', 'No recipients selected. Go to Students tab to select recipients.')}
                        </p>
                      ) : (
                        <div className="flex flex-wrap gap-2">
                          {state.selectedStudents.map((student) => (
                            <span
                              key={student.id}
                              className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm"
                            >
                              {student.display_name}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('messaging.messageContent', 'Message Content')} *
                    </label>
                    <textarea
                      value={state.messageForm.content}
                      onChange={(e) => setState(prev => ({
                        ...prev,
                        messageForm: { ...prev.messageForm, content: e.target.value }
                      }))}
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder={t('messaging.messageContentPlaceholder', 'Enter your message...')}
                    />
                  </div>

                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      onClick={() => setState(prev => ({ ...prev, showNewMessageModal: false }))}
                      className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                    >
                      {t('messaging.cancel', 'Cancel')}
                    </button>
                    <button
                      onClick={handleSendMessage}
                      disabled={!state.messageForm.content.trim() || state.selectedStudents.length === 0}
                      className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                    >
                      <Send className="w-4 h-4" />
                      <span>{t('messaging.sendMessage', 'Send Message')}</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* New Announcement Modal */}
        {state.showAnnouncementModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    {t('messaging.newAnnouncement', 'New Announcement')}
                  </h3>
                  <button
                    onClick={() => setState(prev => ({ ...prev, showAnnouncementModal: false }))}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('messaging.announcementTitle', 'Announcement Title')} *
                    </label>
                    <input
                      type="text"
                      value={state.announcementForm.title}
                      onChange={(e) => setState(prev => ({
                        ...prev,
                        announcementForm: { ...prev.announcementForm, title: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      placeholder={t('messaging.announcementTitlePlaceholder', 'Enter announcement title...')}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('messaging.priority', 'Priority')}
                    </label>
                    <select
                      value={state.announcementForm.priority}
                      onChange={(e) => setState(prev => ({
                        ...prev,
                        announcementForm: { ...prev.announcementForm, priority: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    >
                      <option value="low">{t('messaging.priorityLow', 'Low')}</option>
                      <option value="normal">{t('messaging.priorityNormal', 'Normal')}</option>
                      <option value="high">{t('messaging.priorityHigh', 'High')}</option>
                      <option value="urgent">{t('messaging.priorityUrgent', 'Urgent')}</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('messaging.targetAudience', 'Target Audience')}
                    </label>
                    <select
                      value={state.announcementForm.target_audience}
                      onChange={(e) => setState(prev => ({
                        ...prev,
                        announcementForm: { ...prev.announcementForm, target_audience: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    >
                      <option value="students">{t('messaging.students', 'Students')}</option>
                      <option value="teachers">{t('messaging.teachers', 'Teachers')}</option>
                      <option value="all">{t('messaging.everyone', 'Everyone')}</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('messaging.announcementContent', 'Announcement Content')} *
                    </label>
                    <textarea
                      value={state.announcementForm.content}
                      onChange={(e) => setState(prev => ({
                        ...prev,
                        announcementForm: { ...prev.announcementForm, content: e.target.value }
                      }))}
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      placeholder={t('messaging.announcementContentPlaceholder', 'Enter announcement content...')}
                    />
                  </div>

                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      onClick={() => setState(prev => ({ ...prev, showAnnouncementModal: false }))}
                      className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                    >
                      {t('messaging.cancel', 'Cancel')}
                    </button>
                    <button
                      onClick={handleCreateAnnouncement}
                      disabled={!state.announcementForm.title.trim() || !state.announcementForm.content.trim()}
                      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                    >
                      <Bell className="w-4 h-4" />
                      <span>{t('messaging.createAnnouncement', 'Create Announcement')}</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminMessagingInterface;
